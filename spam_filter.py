import os
import socket
import shutil
import re

# --- Configuration ---
# NOTE: These paths are for WSL. If you run this on Windows, you'll need to change them.
# For example, 'E:\Maildir\cur' on Windows becomes '/mnt/e/Maildir/cur' in WSL.

SOURCE_DIR = "/mnt/e/LuxSciMail/san-INBOX_2017-11-05/"
SPAM_DIR = "/mnt/e/LuxSciMail/san-SPAM_2017-11-05/rez/spam"
HAM_DIR = "/mnt/e/LuxSciMail/san-INBOX_2017-11-05/rez/ham"
SPAM_LOG_FILE = os.path.join(SPAM_DIR, "spam_scores.txt")

# SpamAssassin daemon configuration
SPAMD_HOST = "localhost"
SPAMD_PORT = 783

# --- Helper Functions ---

def get_next_filename(directory):
    """Calculates the next sequential filename (e.g., '1', '2', '3')."""
    return str(len(os.listdir(directory)) + 1)

def check_existing_spam_headers(file_path):
    """
    Check if the email already has X-Spam-Status headers.
    Returns a tuple: (already_processed, is_spam, score) or (False, None, None) if not processed
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            # Read only the headers (until first blank line)
            headers = []
            for line in f:
                line = line.strip()
                if not line:  # Empty line indicates end of headers
                    break
                headers.append(line)

        # Look for X-Spam-Status header
        for header in headers:
            if header.lower().startswith('x-spam-status:'):
                # Parse the header: X-Spam-Status: No, score=-1.0 required=5.0 ...
                # or: X-Spam-Status: Yes, score=15.2 required=5.0 ...
                if 'no,' in header.lower():
                    # Extract score if present
                    score_match = re.search(r'score=([\d.-]+)', header.lower())
                    score = score_match.group(1) if score_match else "0.0"
                    return True, False, score
                elif 'yes,' in header.lower():
                    # Extract score if present
                    score_match = re.search(r'score=([\d.-]+)', header.lower())
                    score = score_match.group(1) if score_match else "5.0"
                    return True, True, score

        return False, None, None

    except Exception as e:
        print(f"Error reading headers from {file_path}: {e}")
        return False, None, None

def check_spam(file_path):
    """
    Checks if an email file is spam using SpamAssassin via WSL spamc command.
    Returns a tuple: (is_spam, score)
    """
    import subprocess

    try:
        print(f"    -> Using WSL spamc command to check spam...")

        # Use WSL spamc command-line client
        # spamc -c checks and returns exit code: 0=ham, 1=spam
        cmd = ['wsl', 'spamc', '-c', '-d', f'{SPAMD_HOST}', '-p', str(SPAMD_PORT), '-u', 'san']

        # Read file and send to spamc via stdin
        with open(file_path, 'rb') as f:
            result = subprocess.run(cmd, input=f.read(), capture_output=True, timeout=120)

        # spamc -c returns: 0=ham, 1=spam
        is_spam = result.returncode == 1

        if result.returncode not in [0, 1]:
            # Error occurred
            error_msg = result.stderr.decode('utf-8', errors='ignore')
            print(f"    -> spamc error (code {result.returncode}): {error_msg}")
            return None, None

        # Get detailed report to extract score
        cmd_report = ['wsl', 'spamc', '-R', '-d', f'{SPAMD_HOST}', '-p', str(SPAMD_PORT), '-u', 'san']

        with open(file_path, 'rb') as f:
            result_report = subprocess.run(cmd_report, input=f.read(), capture_output=True, timeout=120)

        if result_report.returncode in [0, 1]:  # Both ham and spam are valid
            report_text = result_report.stdout.decode('utf-8', errors='ignore')

            # Extract score from report headers
            score_match = re.search(r'X-Spam-Score:\s*([\d.-]+)', report_text)
            if score_match:
                score = score_match.group(1)
            else:
                # Try alternative format in report
                score_match = re.search(r'score=([\d.-]+)', report_text)
                score = score_match.group(1) if score_match else "0.0"

            print(f"    -> SpamAssassin result: {'SPAM' if is_spam else 'HAM'} (Score: {score})")
            return is_spam, score
        else:
            print(f"    -> spamc report error: {result_report.stderr.decode('utf-8', errors='ignore')}")
            return None, None

    except subprocess.TimeoutExpired:
        print(f"    -> Timeout while checking {file_path}")
        return None, None
    except FileNotFoundError:
        print(f"    -> WSL or spamc command not found. Please ensure WSL is installed and spamc is available.")
        return None, None
    except Exception as e:
        print(f"    -> An error occurred while checking {file_path}: {e}")
        return None, None

def process_emails():
    """
    Main function to process all emails in the source directory.
    """
    # Ensure destination directories exist
    os.makedirs(SPAM_DIR, exist_ok=True)
    os.makedirs(HAM_DIR, exist_ok=True)

    print(f"Scanning for email files in {SOURCE_DIR}...")

    for filename in os.listdir(SOURCE_DIR):
        # Skip directories and hidden files
        source_path = os.path.join(SOURCE_DIR, filename)
        if not os.path.isfile(source_path) or filename.startswith('.'):
            continue

        print(f"Processing {source_path}...")

        # First check if the email already has spam headers
        already_processed, is_spam, score = check_existing_spam_headers(source_path)

        if already_processed:
            print(f"  -> Already processed (X-Spam-Status found): {'SPAM' if is_spam else 'HAM'} (Score: {score})")
        else:
            print(f"  -> No X-Spam-Status header found, checking with SpamAssassin daemon...")
            is_spam, score = check_spam(source_path)

            if is_spam is None:
                # An error occurred in check_spam
                continue

        if is_spam:
            # --- Handle Spam ---
            new_name = get_next_filename(SPAM_DIR)
            dest_path = os.path.join(SPAM_DIR, new_name)
            
            print(f"  -> SPAM (Score: {score}). Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)
            
            # Log the spam score
            with open(SPAM_LOG_FILE, 'a') as log_file:
                log_file.write(f"{new_name}: {score}\n")

        else:
            # --- Handle Ham ---
            new_name = get_next_filename(HAM_DIR)
            dest_path = os.path.join(HAM_DIR, new_name)

            print(f"  -> HAM. Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)

    print("\nProcessing complete.")

# --- Main Execution ---
if __name__ == "__main__":
    process_emails()