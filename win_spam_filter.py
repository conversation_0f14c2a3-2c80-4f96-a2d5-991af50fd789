import os
import socket
import shutil
import re
import time

# --- Configuration ---
# NOTE: These paths are configured for Windows.

SOURCE_DIR = r"E:\LuxSciMail\san-INBOX_2017-11-05"
SPAM_DIR = r"E:\LuxSciMail\san-SPAM_2017-11-05\rez\spam"
HAM_DIR = r"E:\LuxSciMail\san-INBOX_2017-11-05\rez\ham"
SPAM_LOG_FILE = os.path.join(SPAM_DIR, "spam_scores.txt")

# SpamAssassin daemon configuration
SPAMD_HOST = "localhost"
SPAMD_PORT = 783

# --- Helper Functions ---

def test_spamd_connection():
    """
    Test connection to SpamAssassin daemon.
    Returns True if connection successful, False otherwise.
    """
    try:
        print(f"Testing connection to SpamAssassin daemon at {SPAMD_HOST}:{SPAMD_PORT}...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5 second timeout for test
        sock.connect((SPAMD_HOST, SPAMD_PORT))
        sock.close()
        print("✓ SpamAssassin daemon connection successful")
        return True
    except ConnectionRefusedError:
        print(f"✗ Connection refused. SpamAssassin daemon is not running on {SPAMD_HOST}:{SPAMD_PORT}")
        return False
    except socket.timeout:
        print(f"✗ Connection timeout. SpamAssassin daemon may be unresponsive")
        return False
    except Exception as e:
        print(f"✗ Connection error: {e}")
        return False

def get_next_filename(directory):
    """Calculates the next sequential filename (e.g., '1', '2', '3')."""
    return str(len(os.listdir(directory)) + 1)

def check_existing_spam_headers(file_path):
    """
    Check if the email already has X-Spam-Status headers.
    Returns a tuple: (already_processed, is_spam, score) or (False, None, None) if not processed
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            # Read only the headers (until first blank line)
            headers = []
            for line in f:
                line = line.strip()
                if not line:  # Empty line indicates end of headers
                    break
                headers.append(line)

        # Look for X-Spam-Status header
        for header in headers:
            if header.lower().startswith('x-spam-status:'):
                # Parse the header: X-Spam-Status: No, score=-1.0 required=5.0 ...
                # or: X-Spam-Status: Yes, score=15.2 required=5.0 ...
                if 'no,' in header.lower():
                    # Extract score if present
                    score_match = re.search(r'score=([\d.-]+)', header.lower())
                    score = score_match.group(1) if score_match else "0.0"
                    return True, False, score
                elif 'yes,' in header.lower():
                    # Extract score if present
                    score_match = re.search(r'score=([\d.-]+)', header.lower())
                    score = score_match.group(1) if score_match else "5.0"
                    return True, True, score

        return False, None, None

    except Exception as e:
        print(f"Error reading headers from {file_path}: {e}")
        return False, None, None

def check_spam(file_path):
    """
    Checks if an email file is spam using SpamAssassin daemon API.
    Returns a tuple: (is_spam, score)
    """
    try:
        # Check file size first
        file_size = os.path.getsize(file_path)
        print(f"    -> File size: {file_size:,} bytes")

        # Skip very large files that might cause timeouts
        if file_size > 10 * 1024 * 1024:  # 10MB limit
            print(f"    -> File too large ({file_size:,} bytes), skipping SpamAssassin check")
            return False, "0.0"  # Assume ham for very large files

        # Read the email content
        with open(file_path, 'rb') as f:  # Read as binary
            email_content = f.read()

        print(f"    -> Connecting to SpamAssassin daemon at {SPAMD_HOST}:{SPAMD_PORT}...")

        # Connect to SpamAssassin daemon
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # Quick connection timeout

        try:
            sock.connect((SPAMD_HOST, SPAMD_PORT))
            print(f"    -> Connected successfully")
        except socket.timeout:
            print(f"    -> Connection timeout after 5 seconds")
            sock.close()
            return None, None
        except ConnectionRefusedError:
            print(f"    -> Connection refused. Is SpamAssassin daemon running on port {SPAMD_PORT}?")
            sock.close()
            return None, None

        # Prepare the request using proper SpamAssassin protocol
        content_length = len(email_content)

        # Use CHECK method to get basic spam/ham result
        request_headers = f"CHECK SPAMC/1.5\r\nContent-length: {content_length}\r\nUser: san\r\n\r\n"
        request_bytes = request_headers.encode('utf-8') + email_content

        print(f"    -> Sending email content ({content_length} bytes) to daemon...")

        # Set timeout based on file size (larger files need more time)
        # Real emails can take much longer due to DNS lookups and complex analysis
        timeout = min(120 + (content_length // 50000), 300)  # 120s base + 1s per 50KB, max 300s (5 minutes)
        sock.settimeout(timeout)
        print(f"    -> Using {timeout}s timeout for this file")

        # Send the request
        sock.sendall(request_bytes)

        print(f"    -> Waiting for response...")

        # Read the response with timeout handling
        response = b""
        start_time = time.time()

        try:
            while time.time() - start_time < timeout:
                try:
                    chunk = sock.recv(1024)
                    if not chunk:
                        break
                    response += chunk
                    # Check if we have a complete response (ends with \r\n\r\n)
                    if b'\r\n\r\n' in response:
                        break
                except socket.timeout:
                    continue  # Keep trying until overall timeout
        except Exception as e:
            print(f"    -> Error reading response: {e}")
            sock.close()
            return None, None

        sock.close()

        if not response:
            print(f"    -> No response received after {timeout}s")
            return None, None

        print(f"    -> Received response ({len(response)} bytes)")

        # Parse the response
        response_text = response.decode('utf-8', errors='ignore')
        lines = response_text.split('\r\n')

        print(f"    -> Response lines: {[line for line in lines if line]}")

        # Look for spam result line: "Spam: False ; 3.3 / 5.0"
        is_spam = False
        score = "0.0"

        for line in lines:
            if line.startswith('Spam:'):
                # Parse line like: "Spam: True ; 15.2 / 5.0" or "Spam: False ; 3.3 / 5.0"
                is_spam = 'True' in line
                parts = line.split(';')
                if len(parts) > 1:
                    score_part = parts[1].strip().split('/')[0].strip()
                    score = score_part
                break

        print(f"    -> SpamAssassin result: {'SPAM' if is_spam else 'HAM'} (Score: {score})")
        return is_spam, score

    except Exception as e:
        print(f"    -> An error occurred while checking {file_path}: {e}")
        return None, None

def process_emails():
    """
    Main function to process all emails in the source directory.
    """
    # Test SpamAssassin daemon connection first
    if not test_spamd_connection():
        print("\nCannot proceed without SpamAssassin daemon connection.")
        print("Please ensure SpamAssassin daemon (spamd) is running.")
        print("You can start it with: spamd -d --pidfile=/var/run/spamd.pid")
        return

    # Ensure destination directories exist
    os.makedirs(SPAM_DIR, exist_ok=True)
    os.makedirs(HAM_DIR, exist_ok=True)

    print(f"\nScanning for email files in {SOURCE_DIR}...")

    for filename in os.listdir(SOURCE_DIR):
        # Skip directories and hidden files
        source_path = os.path.join(SOURCE_DIR, filename)
        if not os.path.isfile(source_path) or filename.startswith('.'):
            continue

        print(f"Processing {source_path}...")

        # First check if the email already has spam headers
        already_processed, is_spam, score = check_existing_spam_headers(source_path)

        if already_processed:
            print(f"  -> Already processed (X-Spam-Status found): {'SPAM' if is_spam else 'HAM'} (Score: {score})")
        else:
            print(f"  -> No X-Spam-Status header found, checking with SpamAssassin daemon...")
            is_spam, score = check_spam(source_path)

            if is_spam is None:
                # An error occurred in check_spam (timeout, connection error, etc.)
                print(f"  -> Skipping file due to error")
                continue

        if is_spam:
            # --- Handle Spam ---
            new_name = get_next_filename(SPAM_DIR)
            dest_path = os.path.join(SPAM_DIR, new_name)
            
            print(f"  -> SPAM (Score: {score}). Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)
            
            # Log the spam score
            with open(SPAM_LOG_FILE, 'a') as log_file:
                log_file.write(f"{new_name}: {score}\n")

        else:
            # --- Handle Ham ---
            new_name = get_next_filename(HAM_DIR)
            dest_path = os.path.join(HAM_DIR, new_name)

            print(f"  -> HAM. Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)

        # Add a small delay between files to avoid overwhelming the daemon
        time.sleep(0.1)  # 100ms delay

    print("\nProcessing complete.")

# --- Main Execution ---
if __name__ == "__main__":
    process_emails()