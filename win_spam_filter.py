import os
import socket
import shutil
import re

# --- Configuration ---
# NOTE: These paths are for WSL. If you run this on Windows, you'll need to change them.
# For example, 'E:\Maildir\cur' on Windows becomes '/mnt/e/Maildir/cur' in WSL.

SOURCE_DIR = "/mnt/e/LuxSciMail/san-INBOX_2017-11-05/"
SPAM_DIR = "/mnt/e/LuxSciMail/san-SPAM_2017-11-05/rez/spam"
HAM_DIR = "/mnt/e/LuxSciMail/san-INBOX_2017-11-05/rez/ham"
SPAM_LOG_FILE = os.path.join(SPAM_DIR, "spam_scores.txt")

# SpamAssassin daemon configuration
SPAMD_HOST = "localhost"
SPAMD_PORT = 783

# --- Helper Functions ---

def get_next_filename(directory):
    """Calculates the next sequential filename (e.g., '1', '2', '3')."""
    return str(len(os.listdir(directory)) + 1)

def check_existing_spam_headers(file_path):
    """
    Check if the email already has X-Spam-Status headers.
    Returns a tuple: (already_processed, is_spam, score) or (False, None, None) if not processed
    """
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            # Read only the headers (until first blank line)
            headers = []
            for line in f:
                line = line.strip()
                if not line:  # Empty line indicates end of headers
                    break
                headers.append(line)

        # Look for X-Spam-Status header
        for header in headers:
            if header.lower().startswith('x-spam-status:'):
                # Parse the header: X-Spam-Status: No, score=-1.0 required=5.0 ...
                # or: X-Spam-Status: Yes, score=15.2 required=5.0 ...
                if 'no,' in header.lower():
                    # Extract score if present
                    score_match = re.search(r'score=([\d.-]+)', header.lower())
                    score = score_match.group(1) if score_match else "0.0"
                    return True, False, score
                elif 'yes,' in header.lower():
                    # Extract score if present
                    score_match = re.search(r'score=([\d.-]+)', header.lower())
                    score = score_match.group(1) if score_match else "5.0"
                    return True, True, score

        return False, None, None

    except Exception as e:
        print(f"Error reading headers from {file_path}: {e}")
        return False, None, None

def check_spam(file_path):
    """
    Checks if an email file is spam using SpamAssassin daemon API.
    Returns a tuple: (is_spam, score)
    """
    try:
        # Read the email content
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            email_content = f.read()

        # Connect to SpamAssassin daemon
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30)  # 30 second timeout
        sock.connect((SPAMD_HOST, SPAMD_PORT))

        # Prepare the request
        content_length = len(email_content.encode('utf-8'))
        request = f"CHECK SPAMC/1.2\r\nContent-length: {content_length}\r\n\r\n{email_content}"

        # Send the request
        sock.sendall(request.encode('utf-8'))

        # Read the response
        response = b""
        while True:
            chunk = sock.recv(4096)
            if not chunk:
                break
            response += chunk

        sock.close()

        # Parse the response
        response_text = response.decode('utf-8', errors='ignore')
        lines = response_text.split('\r\n')

        # Look for the spam status line
        # The response format might be different, let's try multiple patterns
        spam_line = lines[0] if lines else ""

        # Try different response patterns
        # Pattern 1: "Spam: True ; 15.2 / 5.0"
        spam_match = re.search(r'Spam: (True|False) ; ([\d.-]+) / ([\d.-]+)', spam_line)

        if not spam_match:
            # Pattern 2: "SPAMD/1.1 0 EX_OK" followed by spam info
            # Look for spam info in subsequent lines
            for line in lines:
                if 'Spam:' in line:
                    spam_match = re.search(r'Spam: (True|False) ; ([\d.-]+) / ([\d.-]+)', line)
                    if spam_match:
                        break

        if not spam_match:
            # Pattern 3: Look for score in different format
            for line in lines:
                # Try to find score in format like "X-Spam-Score: 2.5"
                score_match = re.search(r'X-Spam-Score:\s*([\d.-]+)', line)
                status_match = re.search(r'X-Spam-Flag:\s*(YES|NO)', line)
                if score_match:
                    score = score_match.group(1)
                    is_spam = status_match.group(1) == 'YES' if status_match else float(score) >= 5.0
                    return is_spam, score

        if spam_match:
            is_spam = spam_match.group(1) == 'True'
            score = spam_match.group(2)
            return is_spam, score
        else:
            print(f"Could not parse SpamAssassin response for {file_path}")
            print(f"Full response: {response_text[:500]}...")  # Show first 500 chars
            return None, None

    except ConnectionRefusedError:
        print("Error: Could not connect to SpamAssassin daemon. Is spamd running?")
        return None, None
    except socket.timeout:
        print(f"Timeout while checking {file_path}")
        return None, None
    except Exception as e:
        print(f"An error occurred while checking {file_path}: {e}")
        return None, None

def process_emails():
    """
    Main function to process all emails in the source directory.
    """
    # Ensure destination directories exist
    os.makedirs(SPAM_DIR, exist_ok=True)
    os.makedirs(HAM_DIR, exist_ok=True)

    print(f"Scanning for email files in {SOURCE_DIR}...")

    for filename in os.listdir(SOURCE_DIR):
        # Skip directories and hidden files
        source_path = os.path.join(SOURCE_DIR, filename)
        if not os.path.isfile(source_path) or filename.startswith('.'):
            continue

        print(f"Processing {source_path}...")

        # First check if the email already has spam headers
        already_processed, is_spam, score = check_existing_spam_headers(source_path)

        if already_processed:
            print(f"  -> Already processed (X-Spam-Status found): {'SPAM' if is_spam else 'HAM'} (Score: {score})")
        else:
            print(f"  -> No X-Spam-Status header found, checking with SpamAssassin daemon...")
            is_spam, score = check_spam(source_path)

            if is_spam is None:
                # An error occurred in check_spam
                continue

        if is_spam:
            # --- Handle Spam ---
            new_name = get_next_filename(SPAM_DIR)
            dest_path = os.path.join(SPAM_DIR, new_name)
            
            print(f"  -> SPAM (Score: {score}). Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)
            
            # Log the spam score
            with open(SPAM_LOG_FILE, 'a') as log_file:
                log_file.write(f"{new_name}: {score}\n")

        else:
            # --- Handle Ham ---
            new_name = get_next_filename(HAM_DIR)
            dest_path = os.path.join(HAM_DIR, new_name)

            print(f"  -> HAM. Moving to {dest_path}")
            
            # Move and rename the file
            shutil.move(source_path, dest_path)

    print("\nProcessing complete.")

# --- Main Execution ---
if __name__ == "__main__":
    process_emails()