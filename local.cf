# SpamAssassin Local Configuration
# This file speeds up Spam<PERSON><PERSON><PERSON> by disabling slow DNS-based checks

# Disable URIBL checks that are causing timeouts
score URIBL_BLOCKED 0
score URIBL_BLACK 0
score URIBL_GREY 0
score URIBL_RED 0
score URIBL_SBL 0
score URIBL_SBL_A 0
score URIBL_CSS 0
score URIBL_CSS_A 0
score URIBL_WS_SURBL 0
score URIBL_MW_SURBL 0
score URIBL_PH_SURBL 0
score URIBL_OB_SURBL 0
score URIBL_AB_SURBL 0
score URIBL_JP_SURBL 0

# Disable other slow DNS-based checks
score RCVD_IN_DNSWL_NONE 0
score RCVD_IN_DNSWL_LOW 0
score RCVD_IN_DNSWL_MED 0
score RCVD_IN_DNSWL_HI 0
score RCVD_IN_SORBS_DUL 0
score RCVD_IN_SORBS_SPAM 0
score RCVD_IN_SORBS_WEB 0
score RCVD_IN_SBL 0
score RCVD_IN_XBL 0
score RCVD_IN_PBL 0

# Reduce DNS timeout to fail faster
rbl_timeout 5

# Skip RB<PERSON> checks entirely if you want maximum speed
# skip_rbl_checks 1

# Use local rules only for maximum speed (uncomment if needed)
# use_razor2 0
# use_pyzor 0
# use_dcc 0
# use_bayes 1

# Set required score (5.0 is default)
required_score 5.0

# Report template
report_safe 0
