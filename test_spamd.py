#!/usr/bin/env python3
"""
SpamAssassin Daemon Connection Test Script
This script helps diagnose SpamAssassin daemon connection issues.
"""

import socket
import sys

# Configuration
SPAMD_HOST = "localhost"
SPAMD_PORT = 783

def test_basic_connection():
    """Test basic TCP connection to SpamAssassin daemon."""
    print(f"Testing basic connection to {SPAMD_HOST}:{SPAMD_PORT}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        sock.connect((SPAMD_HOST, SPAMD_PORT))
        sock.close()
        print("✓ Basic connection successful")
        return True
    except ConnectionRefusedError:
        print("✗ Connection refused - SpamAssassin daemon is not running")
        return False
    except socket.timeout:
        print("✗ Connection timeout - daemon may be unresponsive")
        return False
    except Exception as e:
        print(f"✗ Connection error: {e}")
        return False

def test_spamd_protocol():
    """Test actual SpamAssassin protocol with a simple email."""
    print(f"\nTesting SpamAssassin protocol...")
    
    # Simple test email
    test_email = """From: <EMAIL>
To: <EMAIL>
Subject: Test Email

This is a test email for SpamAssassin.
"""
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(30)
        sock.connect((SPAMD_HOST, SPAMD_PORT))
        
        # Prepare request
        content_length = len(test_email.encode('utf-8'))
        request = f"CHECK SPAMC/1.2\r\nContent-length: {content_length}\r\n\r\n{test_email}"
        
        print(f"Sending test email ({content_length} bytes)...")
        sock.sendall(request.encode('utf-8'))
        
        # Read response
        response = b""
        while True:
            chunk = sock.recv(4096)
            if not chunk:
                break
            response += chunk
        
        sock.close()
        
        response_text = response.decode('utf-8', errors='ignore')
        print(f"✓ Received response ({len(response)} bytes):")
        print("--- Response ---")
        print(response_text[:500])  # Show first 500 chars
        if len(response_text) > 500:
            print("... (truncated)")
        print("--- End Response ---")
        return True
        
    except socket.timeout:
        print("✗ Timeout during protocol test")
        return False
    except Exception as e:
        print(f"✗ Protocol test error: {e}")
        return False

def check_common_issues():
    """Check for common SpamAssassin daemon issues."""
    print(f"\nChecking common issues...")
    
    # Check if running on Windows
    if sys.platform.startswith('win'):
        print("• Running on Windows - make sure SpamAssassin is properly installed")
        print("• Consider using WSL or a Linux VM for SpamAssassin")
    
    # Check alternative ports
    alternative_ports = [783, 11001, 11002]
    print(f"• Checking alternative ports...")
    for port in alternative_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            sock.connect((SPAMD_HOST, port))
            sock.close()
            print(f"  ✓ Found service on port {port}")
        except:
            print(f"  ✗ No service on port {port}")

def main():
    """Main test function."""
    print("SpamAssassin Daemon Connection Test")
    print("=" * 40)
    
    # Test basic connection
    if not test_basic_connection():
        check_common_issues()
        print("\nTroubleshooting steps:")
        print("1. Install SpamAssassin: sudo apt-get install spamassassin spamc")
        print("2. Start daemon: sudo spamd -d --pidfile=/var/run/spamd.pid")
        print("3. Check if running: ps aux | grep spamd")
        print("4. Check port: netstat -tlnp | grep 783")
        return False
    
    # Test protocol
    if not test_spamd_protocol():
        print("\nProtocol test failed - daemon may be misconfigured")
        return False
    
    print("\n✓ All tests passed! SpamAssassin daemon is working correctly.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
