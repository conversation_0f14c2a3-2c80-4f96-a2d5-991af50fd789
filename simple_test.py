#!/usr/bin/env python3

import socket
import time

def test_spamd():
    """Simple test of SpamAssassin daemon"""
    
    # Larger test email to simulate real emails
    test_email = """From: <EMAIL>
To: <EMAIL>
Subject: Test Email with More Content
Date: Mon, 1 Jan 2024 12:00:00 +0000
Message-ID: <<EMAIL>>
MIME-Version: 1.0
Content-Type: text/plain; charset=utf-8

This is a test email with more content to simulate real emails.

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod
tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
velit esse cillum dolore eu fugiat nulla pariatur.

Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde
omnis iste natus error sit voluptatem accusantium doloremque laudantium.

Best regards,
Test User
"""
    
    print("Testing SpamAssassin daemon...")
    
    # Connect to SpamAssassin daemon
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(10)
    
    try:
        sock.connect(('localhost', 783))
        print("✓ Connected to SpamAssassin daemon")
        
        # Prepare the request
        email_bytes = test_email.encode('utf-8')
        content_length = len(email_bytes)
        
        request_headers = f"CHECK SPAMC/1.5\r\nContent-length: {content_length}\r\nUser: san\r\n\r\n"
        request_bytes = request_headers.encode('utf-8') + email_bytes
        
        print(f"Sending request ({len(request_bytes)} bytes)...")
        print(f"Headers: {repr(request_headers)}")
        
        # Send the request
        sock.sendall(request_bytes)
        print("✓ Request sent")
        
        # Read response
        print("Waiting for response...")
        response = b""
        start_time = time.time()
        
        while time.time() - start_time < 10:  # 10 second timeout
            try:
                chunk = sock.recv(1024)
                if not chunk:
                    break
                response += chunk
                print(f"Received chunk: {len(chunk)} bytes")
            except socket.timeout:
                print("Timeout waiting for response")
                break
        
        sock.close()
        
        if response:
            print(f"\n✓ Received response ({len(response)} bytes):")
            response_text = response.decode('utf-8', errors='ignore')
            print("Raw response:")
            print(repr(response_text))
            print("\nFormatted response:")
            for i, line in enumerate(response_text.split('\r\n')):
                print(f"Line {i}: {repr(line)}")
        else:
            print("✗ No response received")
            
    except Exception as e:
        print(f"✗ Error: {e}")
        sock.close()

if __name__ == "__main__":
    test_spamd()
